import { useEffect, useState, useRef } from 'react';
import { useAuthStore } from '../stores/authStore';
import { useRolewiseStore } from '../stores/rolewiseStore';
import { Link } from 'react-router-dom';
import {
  dashboardContainer,
  titleStyle,
  cardStyle,
  headerStyle,
  contentSectionStyle,
  sectionTitleStyle,
  insightsListStyle,
  insightItemStyle,
  insightIconWarningStyle,
  insightIconSuccessStyle,
  insightIconInfoStyle,
  errorMessageStyle,
  testStyle,
  statusBadgeStyle,
  processingStatusStyle,
  statusPendingStyle,
  statusFetchingStyle,
  statusProcessingStyle,
  statusInferringStyle,
  statusCompletedStyle,
  statusFailedStyle,
} from './dashboardStyles.css.ts';

const Dashboard = () => {
  const { user, tenantId } = useAuthStore();
  const {
    roles,
    processTenantData,
    loading,
    error,
    acknowledgeError,
    currentJobId,
    currentJobStatus,
    checkJobStatus
  } = useRolewiseStore();

  const [runInference, setRunInference] = useState(true);
  const [forceRefresh, setForceRefresh] = useState(false);
  const initialLoadRef = useRef(true);

  // Function to get the status badge style based on job status
  const getStatusBadgeStyle = (status: string | null) => {
    switch (status) {
      case 'pending':
        return statusPendingStyle;
      case 'fetching':
        return statusFetchingStyle;
      case 'processing':
        return statusProcessingStyle;
      case 'inferring':
        return statusInferringStyle;
      case 'completed':
        return statusCompletedStyle;
      case 'failed':
        return statusFailedStyle;
      default:
        return statusPendingStyle;
    }
  };

  // Function to get a human-readable status message
  const getStatusMessage = (status: string | null) => {
    switch (status) {
      case 'pending':
        return 'Preparing to process data...';
      case 'fetching':
        return 'Fetching data from Microsoft Entra ID...';
      case 'processing':
        return 'Processing tenant data...';
      case 'inferring':
        return 'Inferring roles from user data...';
      case 'completed':
        return 'Processing completed successfully';
      case 'failed':
        return 'Processing failed';
      default:
        return 'Unknown status';
    }
  };

  // Check job status periodically if we have a job ID
  useEffect(() => {
    if (currentJobId && currentJobStatus && currentJobStatus !== 'completed' && currentJobStatus !== 'failed') {
      const interval = setInterval(async () => {
        const job = await checkJobStatus(currentJobId);
        if (job?.status === 'completed' || job?.status === 'failed') {
          clearInterval(interval);
        }
      }, 3000);

      return () => clearInterval(interval);
    }
  }, [currentJobId, currentJobStatus, checkJobStatus]);

  // Initial data load - only run once when component mounts and user/tenantId are available
  useEffect(() => {
    if (user && tenantId && initialLoadRef.current) {
      console.log(
        'Initial load useEffect triggering processTenantData with tenantId:',
        tenantId,
        'at:',
        new Date().toISOString()
      );
      initialLoadRef.current = false; // Prevent this effect from running again
      processTenantData(tenantId, runInference, false);
    }
  }, [user, tenantId, processTenantData, runInference]);

  // Only react to changes in runInference if triggered by a button click
  useEffect(() => {
    // Do nothing - we'll manually call processTenantData when needed
  }, [runInference]);

  if (!user) {
    return <div className={testStyle}>Please log in to view the dashboard.</div>;
  }

  if (loading && !currentJobId) {
    return <div className={dashboardContainer}>Loading dashboard data...</div>;
  }

  if (error) {
    return (
      <div className={dashboardContainer}>
        <div className={headerStyle}>
          <h1 className={titleStyle}>Dashboard</h1>
        </div>
        <div className={errorMessageStyle}>
          <p>Error loading dashboard data: {error}</p>
          <p>This may be because the Entra ID integration is not set up for your tenant.</p>
          <p>Please contact your administrator to set up the integration.</p>
          <button
            onClick={() => {
              acknowledgeError();
              processTenantData(tenantId, runInference, false);
            }}
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={dashboardContainer}>
      <div className={headerStyle}>
        <h1 className={titleStyle}>Dashboard</h1>
      </div>

      {/* Processing Status Card */}
      {currentJobId && currentJobStatus && (
        <div className={`${cardStyle} ${contentSectionStyle}`}>
          <h2 className={sectionTitleStyle}>
            Processing Status
            <Link
              to="/processing-status"
              style={{
                fontSize: '0.8rem',
                marginLeft: '0.5rem',
                color: '#3b82f6',
                textDecoration: 'none'
              }}
            >
              View All Jobs →
            </Link>
          </h2>
          <div className={processingStatusStyle}>
            <div>
              <span className={getStatusBadgeStyle(currentJobStatus)}>
                {currentJobStatus}
              </span>
              <span>{getStatusMessage(currentJobStatus)}</span>
            </div>
            {currentJobStatus !== 'completed' && currentJobStatus !== 'failed' && (
              <div>Processing your tenant data. This may take a few moments...</div>
            )}
            {currentJobStatus === 'completed' && (
              <div>Your tenant data has been processed successfully.</div>
            )}
            {currentJobStatus === 'failed' && (
              <div>
                There was an error processing your tenant data. Please try again or contact support.
              </div>
            )}
            <div style={{ marginTop: '0.5rem', fontSize: '0.8rem' }}>
              <Link to="/processing-status">View detailed processing status and history</Link>
            </div>
          </div>
        </div>
      )}

      {/* Access Insights Card */}
      <div className={`${cardStyle} ${contentSectionStyle}`}>
        <h2 className={sectionTitleStyle}>Access Insights</h2>
        <div style={{ display: 'flex', gap: '0.5rem', marginBottom: '1rem', flexWrap: 'wrap' }}>
          <button
            onClick={() => {
              if (user && tenantId) {
                console.log('Refresh button clicked, calling processTenantData with tenantId:', tenantId);
                processTenantData(tenantId, runInference, false);
              }
            }}
            disabled={loading || !!error || (currentJobStatus && currentJobStatus !== 'completed' && currentJobStatus !== 'failed')}
          >
            Refresh Data
          </button>

          <button
            onClick={() => {
              if (user && tenantId) {
                console.log('Force refresh button clicked, calling processTenantData with tenantId:', tenantId);
                setForceRefresh(true);
                processTenantData(tenantId, runInference, true).finally(() => {
                  setForceRefresh(false);
                });
              }
            }}
            disabled={loading || !!error || (currentJobStatus && currentJobStatus !== 'completed' && currentJobStatus !== 'failed')}
            style={{ backgroundColor: forceRefresh ? '#f97316' : '' }}
          >
            Force Refresh (Skip Cache)
          </button>

          <label style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
            <input
              type="checkbox"
              checked={runInference}
              onChange={(e) => setRunInference(e.target.checked)}
              disabled={loading || (currentJobStatus && currentJobStatus !== 'completed' && currentJobStatus !== 'failed')}
            />
            Run Role Inference
          </label>
        </div>

        {loading && !currentJobId && <p>Loading insights...</p>}
        {error && <p>Error: {error}</p>}

        <ul className={insightsListStyle}>
          {roles.length === 0 ? (
            <li className={insightItemStyle}>
              <span className={insightIconInfoStyle}>ℹ️</span>
              <span>No insights available yet. Please set up Entra ID integration.</span>
            </li>
          ) : (
            roles.map((role, index) => (
              <li key={role.id} className={insightItemStyle}>
                <span
                  className={
                    index % 3 === 0
                      ? insightIconWarningStyle
                      : index % 3 === 1
                      ? insightIconSuccessStyle
                      : insightIconInfoStyle
                  }
                >
                  {index % 3 === 0 ? '⚠️' : index % 3 === 1 ? '✅' : 'ℹ️'}
                </span>
                <span>
                  Role: {role.name} - {role.description}
                </span>
              </li>
            ))
          )}
        </ul>
      </div>
    </div>
  );
};

export default Dashboard;