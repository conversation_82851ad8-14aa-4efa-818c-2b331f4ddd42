import { style, keyframes } from '@vanilla-extract/css';

// Define the spin animation (equivalent to Tailwind's animate-spin)
const spin = keyframes({
  '0%': { transform: 'rotate(0deg)' },
  '100%': { transform: 'rotate(360deg)' },
});

export const loadingSpinnerStyle = style({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  height: '100%', // Equivalent to h-full
  width: '100%', // Equivalent to w-full
});

// Style for the spinner itself (replaces animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600)
export const spinnerStyle = style({
  animation: `${spin} 1s linear infinite`, // Equivalent to animate-spin
  borderRadius: '9999px', // rounded-full
  height: '3rem', // h-12
  width: '3rem', // w-12
  borderTopWidth: '2px', // border-t-2
  borderBottomWidth: '2px', // border-b-2
  borderLeftWidth: '2px',
  borderRightWidth: '2px',
  borderStyle: 'solid',
  borderColor: 'transparent', // Default border color
  borderTopColor: '#2563eb', // border-primary-600
  borderBottomColor: '#2563eb', // border-primary-600
});