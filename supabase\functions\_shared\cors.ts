export const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  }
// For production, use a more specific origin:
// const corsHeaders = {
//   'Access-Control-Allow-Origin': process.env.ALLOWED_ORIGIN || 'https://your-production-domain.com',
//   'Access-Control-Allow-Methods': 'POST, OPTIONS',
//   'Access-Control-Allow-Headers': 'Authorization, Content-Type, x-client-info, apikey',
//   'Access-Control-Max-Age': '86400',
// };