import { style } from '@vanilla-extract/css';

export const container = style({
  padding: '2rem',
  maxWidth: '800px',
  margin: '0 auto',
  display: 'block',
  visibility: 'visible',
  opacity: 1,
  position: 'relative',
  zIndex: 1,
});

export const title = style({
  fontSize: '2rem',
  fontWeight: 'bold',
  marginBottom: '2rem',
  color: '#1a1a1a',
  display: 'block',
  visibility: 'visible',
  opacity: 1,
});

export const content = style({
  backgroundColor: 'white',
  padding: '2rem',
  borderRadius: '0.5rem',
  boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
  display: 'block',
  visibility: 'visible',
  opacity: 1,
  position: 'relative',
});

export const formGroup = style({
  marginBottom: '1.5rem',
  display: 'block',
  visibility: 'visible',
  opacity: 1,
});

export const label = style({
  display: 'block',
  marginBottom: '0.5rem',
  fontWeight: '500',
  color: '#374151',
  visibility: 'visible',
  opacity: 1,
});

export const input = style({
  width: '100%',
  padding: '0.75rem',
  borderRadius: '0.375rem',
  border: '1px solid #d1d5db',
  backgroundColor: 'white',
  fontSize: '1rem',
  display: 'block',
  visibility: 'visible',
  opacity: 1,
  ':focus': {
    outline: 'none',
    borderColor: '#3b82f6',
    boxShadow: '0 0 0 3px rgba(59, 130, 246, 0.1)',
  },
  ':disabled': {
    backgroundColor: '#f3f4f6',
    cursor: 'not-allowed',
  },
});

export const buttonGroup = style({
  display: 'flex',
  gap: '1rem',
  marginTop: '2rem',
  visibility: 'visible',
  opacity: 1,
});

export const button = style({
  padding: '0.75rem 1.5rem',
  borderRadius: '0.375rem',
  fontWeight: '500',
  fontSize: '1rem',
  cursor: 'pointer',
  transition: 'all 0.2s',
  display: 'inline-block',
  visibility: 'visible',
  opacity: 1,
  ':disabled': {
    opacity: '0.5',
    cursor: 'not-allowed',
  },
  selectors: {
    '&:not(:disabled):hover': {
      transform: 'translateY(-1px)',
    },
  },
});

export const loading = style({
  color: '#6b7280',
  marginBottom: '1rem',
  display: 'block',
  visibility: 'visible',
  opacity: 1,
});

export const result = style({
  marginTop: '1.5rem',
  padding: '1rem',
  borderRadius: '0.375rem',
  fontSize: '1rem',
  display: 'block',
  visibility: 'visible',
  opacity: 1,
});

export const success = style({
  backgroundColor: '#ecfdf5',
  color: '#065f46',
  border: '1px solid #a7f3d0',
  display: 'block',
  visibility: 'visible',
  opacity: 1,
});

export const error = style({
  backgroundColor: '#fef2f2',
  color: '#991b1b',
  border: '1px solid #fecaca',
  display: 'block',
  visibility: 'visible',
  opacity: 1,
});