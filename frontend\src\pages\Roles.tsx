import { useState, useEffect } from 'react';
import { useRolewiseStore } from '../stores/rolewiseStore';
import { RolewiseRole } from '../types/rolewise';
import RoleEditModal from '../components/RoleEditModal';
import * as styles from './rolesStyles.css.ts';

const Roles = () => {
  const { roles, loading, error, updateRole } = useRolewiseStore();
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [selectedRole, setSelectedRole] = useState<string | null>(null);
  const [editingRole, setEditingRole] = useState<RolewiseRole | null>(null);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);

  const filteredRoles = roles.filter(
    (role) =>
      role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      role.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleEditClick = (e: React.MouseEvent, role: RolewiseRole) => {
    e.stopPropagation(); // Prevent selecting the role when clicking edit
    setEditingRole(role);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setEditingRole(null);
  };

  const handleSaveRole = async (updatedRole: RolewiseRole, feedback?: string) => {
    await updateRole(updatedRole, feedback);
  };

  return (
    <div className={styles.container}>
      <h1 className={styles.title}>Roles</h1>
      {error && <div className={styles.error}>{error}</div>}
      {loading ? (
        <div>Loading...</div>
      ) : (
        <div className={styles.content}>
          <div className={styles.filters}>
            <input
              type="text"
              placeholder="Search roles..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={styles.searchInput}
            />
          </div>
          <div className={styles.roleList}>
            {filteredRoles.map((role) => (
              <div
                key={role.id}
                className={`${styles.roleCard} ${selectedRole === role.id ? styles.selected : ''}`}
                onClick={() => setSelectedRole(role.id)}
              >
                <div className={styles.roleInfo}>
                  <h3 className={styles.roleName}>{role.name}</h3>
                  <p className={styles.roleDescription}>{role.description}</p>
                  {role.source && (
                    <div className={styles.roleSource}>
                      Source: <span className={role.source === 'manual' ? styles.manualSource : styles.inferredSource}>
                        {role.source === 'manual' ? 'Manually Defined' : 'AI Inferred'}
                      </span>
                    </div>
                  )}
                  {role.confidenceScore !== undefined && (
                    <div className={styles.confidenceScore}>
                      Confidence: {(role.confidenceScore * 100).toFixed(0)}%
                    </div>
                  )}
                </div>
                <div className={styles.roleActions}>
                  <button
                    className={styles.editButton}
                    onClick={(e) => handleEditClick(e, role)}
                  >
                    Edit
                  </button>
                </div>
              </div>
            ))}
          </div>

          {/* Role Edit Modal */}
          <RoleEditModal
            role={editingRole}
            isOpen={isModalOpen}
            onClose={handleCloseModal}
            onSave={handleSaveRole}
          />
        </div>
      )}
    </div>
  );
};

export default Roles;