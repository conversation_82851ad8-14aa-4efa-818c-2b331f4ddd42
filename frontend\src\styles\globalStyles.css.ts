// src/styles/globalStyles.css.ts
import { style, globalStyle } from '@vanilla-extract/css';

// Global styles from index.css
globalStyle('body', {
  backgroundColor: '#f9fafb', // bg-gray-50
  color: '#1f2937', // text-gray-900
});

// Button styles
export const btnStyle = style({
  padding: '0.5rem 1rem',
  borderRadius: '0.375rem',
  fontWeight: '500',
  transition: 'background-color 0.2s',
});

export const btnPrimaryStyle = style({
  backgroundColor: '#3b82f6', // bg-primary-600
  color: 'white',
  ':hover': {
    backgroundColor: '#2563eb', // hover:bg-primary-700
  },
});

export const btnOutlineStyle = style({
  border: '1px solid #d1d5db', // border-gray-300
  backgroundColor: 'white',
  color: '#374151', // text-gray-700
  ':hover': {
    backgroundColor: '#f3f4f6', // hover:bg-gray-50
  },
});

// Card style (already defined in dashboardStyles.css.ts, but keeping here for global use)
export const cardStyle = style({
  backgroundColor: 'white',
  borderRadius: '0.375rem',
  boxShadow: '0 1px 2px rgba(0, 0, 0, 0.1)',
  padding: '1.5rem', // p-6
});

// Input styles
export const inputStyle = style({
  width: '100%',
  padding: '0.5rem',
  border: '1px solid #d1d5db', // border-gray-300
  borderRadius: '0.375rem',
  ':focus': {
    outline: '2px solid #3b82f6', // focus:ring-2 focus:ring-primary-500
    borderColor: 'transparent',
  },
});