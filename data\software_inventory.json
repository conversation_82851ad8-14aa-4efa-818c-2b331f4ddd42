[{"application_name": "HR Management System", "description": "An internal platform for managing employee records, payroll, and benefits.", "primary_business_owner": "<PERSON>", "primary_technical_owner": "<PERSON>", "department": "Human Resources", "compliance_requirements": ["GDPR", "SOC 2"], "access_permissions": ["HR Admin", "Payroll Specialist", "Compliance Officer"]}, {"application_name": "Financial Reporting Suite", "description": "A financial reporting and analytics tool used for budget planning and audits.", "primary_business_owner": "<PERSON>", "primary_technical_owner": "<PERSON>", "department": "Finance", "compliance_requirements": ["SOX", "PCI DSS"], "access_permissions": ["Finance Manager", "Auditor", "CFO"]}, {"application_name": "IT Service Desk", "description": "A ticketing system for IT support and issue tracking.", "primary_business_owner": "<PERSON>", "primary_technical_owner": "<PERSON>", "department": "IT", "compliance_requirements": ["ISO 27001"], "access_permissions": ["IT Support", "System Administrator", "Security Analyst"]}, {"application_name": "Customer Relationship Management (CRM)", "description": "A CRM tool used for managing customer interactions and sales tracking.", "primary_business_owner": "<PERSON>", "primary_technical_owner": "<PERSON>", "department": "Sales", "compliance_requirements": ["GDPR"], "access_permissions": ["Sales Manager", "Account Executive", "Marketing Analyst"]}, {"application_name": "Product Lifecycle Management (PLM)", "description": "A system for managing product design, development, and manufacturing workflows.", "primary_business_owner": "<PERSON>", "primary_technical_owner": "<PERSON>", "department": "Engineering", "compliance_requirements": ["ISO 9001"], "access_permissions": ["Product Manager", "Design Engineer", "Quality Assurance"]}, {"application_name": "Corporate Email Platform", "description": "Enterprise email system for internal and external communications.", "primary_business_owner": "<PERSON>", "primary_technical_owner": "<PERSON>", "department": "IT", "compliance_requirements": ["ISO 27001", "HIPAA"], "access_permissions": ["All Employees", "IT Admin"]}, {"application_name": "Document Management System", "description": "A secure repository for storing and managing corporate documents.", "primary_business_owner": "<PERSON>", "primary_technical_owner": "<PERSON>", "department": "Legal", "compliance_requirements": ["GDPR", "SOC 2"], "access_permissions": ["Legal Counsel", "Compliance Officer", "Records Manager"]}, {"application_name": "Inventory Tracking System", "description": "A tool for monitoring and managing company inventory and supply chain data.", "primary_business_owner": "<PERSON>", "primary_technical_owner": "<PERSON>", "department": "Operations", "compliance_requirements": ["ISO 28000"], "access_permissions": ["Supply Chain Manager", "Warehouse Supervisor", "Logistics Coordinator"]}, {"application_name": "Marketing Automation Platform", "description": "A system for automating marketing campaigns and tracking customer engagement.", "primary_business_owner": "<PERSON>", "primary_technical_owner": "<PERSON>", "department": "Marketing", "compliance_requirements": ["GDPR", "CAN-SPAM"], "access_permissions": ["Marketing Director", "Campaign Manager", "Data Analyst"]}, {"application_name": "Sales Analytics Dashboard", "description": "A business intelligence tool for analyzing sales data and performance metrics.", "primary_business_owner": "<PERSON>", "primary_technical_owner": "<PERSON>", "department": "Sales", "compliance_requirements": ["GDPR"], "access_permissions": ["Sales VP", "Regional Sales Manager", "Sales Analyst"]}]