import { style } from '@vanilla-extract/css';

export const modalOverlay = style({
  position: 'fixed',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  backgroundColor: 'rgba(0, 0, 0, 0.5)',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  zIndex: 1000,
});

export const modalContent = style({
  backgroundColor: 'white',
  borderRadius: '0.5rem',
  width: '90%',
  maxWidth: '600px',
  maxHeight: '90vh',
  overflow: 'auto',
  boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
});

export const modalHeader = style({
  padding: '1rem',
  borderBottom: '1px solid #e5e7eb',
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
});

export const modalTitle = style({
  margin: 0,
  fontSize: '1.25rem',
  fontWeight: 'bold',
  color: '#111827',
});

export const closeButton = style({
  background: 'none',
  border: 'none',
  fontSize: '1.5rem',
  cursor: 'pointer',
  color: '#6b7280',
  ':hover': {
    color: '#111827',
  },
});

export const modalBody = style({
  padding: '1rem',
});

export const modalFooter = style({
  padding: '1rem',
  borderTop: '1px solid #e5e7eb',
  display: 'flex',
  justifyContent: 'flex-end',
  gap: '0.5rem',
});

export const formGroup = style({
  marginBottom: '1rem',
});

export const formLabel = style({
  display: 'block',
  marginBottom: '0.5rem',
  fontWeight: '500',
  color: '#374151',
});

export const formInput = style({
  width: '100%',
  padding: '0.5rem',
  borderRadius: '0.375rem',
  border: '1px solid #d1d5db',
  fontSize: '1rem',
  ':focus': {
    outline: 'none',
    borderColor: '#3b82f6',
    boxShadow: '0 0 0 3px rgba(59, 130, 246, 0.1)',
  },
});

export const formTextarea = style({
  width: '100%',
  padding: '0.5rem',
  borderRadius: '0.375rem',
  border: '1px solid #d1d5db',
  fontSize: '1rem',
  resize: 'vertical',
  ':focus': {
    outline: 'none',
    borderColor: '#3b82f6',
    boxShadow: '0 0 0 3px rgba(59, 130, 246, 0.1)',
  },
});

export const formRange = style({
  width: '100%',
  height: '0.5rem',
  borderRadius: '0.25rem',
  appearance: 'none',
  backgroundColor: '#e5e7eb',
  '::-webkit-slider-thumb': {
    appearance: 'none',
    width: '1rem',
    height: '1rem',
    borderRadius: '50%',
    backgroundColor: '#3b82f6',
    cursor: 'pointer',
  },
  '::-moz-range-thumb': {
    width: '1rem',
    height: '1rem',
    borderRadius: '50%',
    backgroundColor: '#3b82f6',
    cursor: 'pointer',
  },
});

export const saveButton = style({
  padding: '0.5rem 1rem',
  backgroundColor: '#3b82f6',
  color: 'white',
  border: 'none',
  borderRadius: '0.375rem',
  fontWeight: '500',
  cursor: 'pointer',
  ':hover': {
    backgroundColor: '#2563eb',
  },
  ':disabled': {
    backgroundColor: '#93c5fd',
    cursor: 'not-allowed',
  },
});

export const cancelButton = style({
  padding: '0.5rem 1rem',
  backgroundColor: 'white',
  color: '#374151',
  border: '1px solid #d1d5db',
  borderRadius: '0.375rem',
  fontWeight: '500',
  cursor: 'pointer',
  ':hover': {
    backgroundColor: '#f3f4f6',
  },
  ':disabled': {
    opacity: 0.5,
    cursor: 'not-allowed',
  },
});

export const errorMessage = style({
  backgroundColor: '#fee2e2',
  color: '#b91c1c',
  padding: '0.75rem',
  borderRadius: '0.375rem',
  marginBottom: '1rem',
  fontSize: '0.875rem',
});
