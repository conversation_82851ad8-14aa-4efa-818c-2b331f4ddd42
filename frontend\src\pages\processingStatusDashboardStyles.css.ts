import { style, keyframes } from '@vanilla-extract/css';

// Container styles
export const dashboardContainer = style({
  padding: '1rem',
  paddingTop: '5rem', // Clear navbar (4rem) + extra space
  backgroundColor: '#f9fafb',
  minHeight: '100vh',
  '@media': {
    'screen and (min-width: 768px)': {
      padding: '1.5rem',
      paddingLeft: 'calc(4rem + 1.5rem)', // Collapsed sidebar
      paddingTop: '5rem', // Keep consistent with navbar
    },
    'screen and (min-width: 1024px)': {
      paddingLeft: 'calc(16rem + 1.5rem)', // Expanded sidebar
    },
  },
});

// Title styles
export const titleStyle = style({
  fontSize: '1.25rem', // Reduced from 1.5rem for mobile
  fontWeight: 'bold',
  color: '#1f2937',
  '@media': {
    'screen and (min-width: 768px)': {
      fontSize: '1.5rem', // Original size for larger screens
    },
  },
});

// Header styles
export const headerStyle = style({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  marginBottom: '1rem', // Reduced from 1.5rem
  '@media': {
    'screen and (min-width: 768px)': {
      marginBottom: '1.5rem',
    },
  },
});

// Card styles
export const cardStyle = style({
  padding: '0.75rem', // Reduced from 1rem
  borderRadius: '0.375rem',
  boxShadow: '0 1px 2px rgba(0, 0, 0, 0.1)',
  backgroundColor: 'white',
  width: '100%', // Ensure cards take full width on mobile
  marginBottom: '1rem',
  '@media': {
    'screen and (min-width: 768px)': {
      padding: '1rem', // Restore original padding
      marginBottom: '1.5rem',
    },
  },
});

// Section title styles
export const sectionTitleStyle = style({
  fontSize: '1rem', // Reduced from 1.125rem
  fontWeight: '600',
  marginBottom: '0.75rem', // Reduced from 1rem
  '@media': {
    'screen and (min-width: 768px)': {
      fontSize: '1.125rem',
      marginBottom: '1rem',
    },
  },
});

// Error message styles
export const errorMessageStyle = style({
  padding: '1.5rem',
  margin: '1rem 0',
  backgroundColor: '#fee2e2',
  border: '1px solid #ef4444',
  borderRadius: '0.375rem',
  color: '#991b1b',
});

// Loading styles
export const loadingStyle = style({
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  padding: '2rem',
  color: '#6b7280',
});

// Job list styles
export const jobListStyle = style({
  display: 'flex',
  flexDirection: 'column',
  gap: '0.75rem',
});

// Job item styles
export const jobItemStyle = style({
  padding: '0.75rem',
  borderRadius: '0.375rem',
  backgroundColor: '#f3f4f6',
  display: 'flex',
  flexDirection: 'column',
  gap: '0.5rem',
  cursor: 'pointer',
  transition: 'background-color 0.2s',
  ':hover': {
    backgroundColor: '#e5e7eb',
  },
});

export const selectedJobItemStyle = style([
  jobItemStyle,
  {
    backgroundColor: '#dbeafe',
    borderLeft: '3px solid #3b82f6',
    ':hover': {
      backgroundColor: '#bfdbfe',
    },
  },
]);

// Job details styles
export const jobDetailsStyle = style({
  display: 'flex',
  flexDirection: 'column',
  gap: '0.5rem',
});

// Job metadata styles
export const metadataStyle = style({
  display: 'grid',
  gridTemplateColumns: '1fr 1fr',
  gap: '0.5rem',
  '@media': {
    'screen and (min-width: 768px)': {
      gridTemplateColumns: '1fr 1fr 1fr',
    },
  },
});

export const metadataItemStyle = style({
  display: 'flex',
  flexDirection: 'column',
  padding: '0.5rem',
  backgroundColor: '#f9fafb',
  borderRadius: '0.25rem',
});

export const metadataLabelStyle = style({
  fontSize: '0.75rem',
  color: '#6b7280',
  fontWeight: '500',
});

export const metadataValueStyle = style({
  fontSize: '0.875rem',
  color: '#1f2937',
  fontWeight: '600',
});

// Progress bar styles
export const progressBarContainerStyle = style({
  width: '100%',
  height: '0.5rem',
  backgroundColor: '#e5e7eb',
  borderRadius: '9999px',
  overflow: 'hidden',
  marginTop: '0.5rem',
});

export const progressBarStyle = style({
  height: '100%',
  backgroundColor: '#3b82f6',
  borderRadius: '9999px',
  transition: 'width 0.3s ease',
});

// Status badge styles
export const statusBadgeStyle = style({
  display: 'inline-flex',
  alignItems: 'center',
  padding: '0.25rem 0.5rem',
  borderRadius: '9999px',
  fontWeight: '500',
  fontSize: '0.75rem',
  textTransform: 'uppercase',
});

export const statusPendingStyle = style([
  statusBadgeStyle,
  {
    backgroundColor: '#e5e7eb',
    color: '#4b5563',
  },
]);

export const statusFetchingStyle = style([
  statusBadgeStyle,
  {
    backgroundColor: '#dbeafe',
    color: '#1e40af',
  },
]);

export const statusProcessingStyle = style([
  statusBadgeStyle,
  {
    backgroundColor: '#fef3c7',
    color: '#92400e',
  },
]);

export const statusInferringStyle = style([
  statusBadgeStyle,
  {
    backgroundColor: '#d1fae5',
    color: '#065f46',
  },
]);

export const statusCompletedStyle = style([
  statusBadgeStyle,
  {
    backgroundColor: '#dcfce7',
    color: '#166534',
  },
]);

export const statusFailedStyle = style([
  statusBadgeStyle,
  {
    backgroundColor: '#fee2e2',
    color: '#991b1b',
  },
]);

// Refresh button styles
export const refreshButtonStyle = style({
  padding: '0.5rem 1rem',
  backgroundColor: '#3b82f6',
  color: 'white',
  borderRadius: '0.375rem',
  border: 'none',
  cursor: 'pointer',
  fontWeight: '500',
  transition: 'background-color 0.2s',
  ':hover': {
    backgroundColor: '#2563eb',
  },
  ':disabled': {
    backgroundColor: '#93c5fd',
    cursor: 'not-allowed',
  },
});

// Define the spin animation
const spin = keyframes({
  '0%': { transform: 'rotate(0deg)' },
  '100%': { transform: 'rotate(360deg)' },
});

// Spinner styles
export const spinnerStyle = style({
  animation: `${spin} 1s linear infinite`,
  borderRadius: '9999px',
  height: '1rem',
  width: '1rem',
  borderWidth: '2px',
  borderStyle: 'solid',
  borderColor: 'transparent',
  borderTopColor: 'white',
  borderBottomColor: 'white',
  display: 'inline-block',
  marginRight: '0.5rem',
});
