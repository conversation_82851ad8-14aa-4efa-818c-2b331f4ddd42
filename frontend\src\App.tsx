import React, { Suspense } from 'react';
import { Routes, Route, Navigate, Outlet } from 'react-router-dom';
import ErrorBoundary from './components/ErrorBoundary';
import Sidebar from './components/Sidebar';
import Navbar from './components/Navbar';
import { ProtectedLayout } from './components/ClerkAuth';
import * as styles from './App.css';

// Lazy load pages
const Login = React.lazy(() => import('./pages/Login'));
const SignUp = React.lazy(() => import('./pages/SignUp'));
const Dashboard = React.lazy(() => import('./pages/Dashboard'));
const Settings = React.lazy(() => import('./pages/Settings'));
const Users = React.lazy(() => import('./pages/Users'));
const Roles = React.lazy(() => import('./pages/Roles'));
const Permissions = React.lazy(() => import('./pages/Permissions'));
const AccessInsights = React.lazy(() => import('./pages/AccessInsights'));
const IntegrationSetup = React.lazy(() => import('./pages/IntegrationSetup'));
const ProcessingStatusDashboard = React.lazy(() => import('./pages/ProcessingStatusDashboard'));
const NotFound = React.lazy(() => import('./pages/NotFound'));

// Custom ProtectedLayout that uses Clerk's authentication
const ClerkProtectedLayout = () => {
  return (
    <ProtectedLayout>
      <div className={styles.appContainer}>
        <Navbar />
        <div className={styles.mainLayout}>
          <Sidebar />
          <main className={styles.mainContent}>
            <Outlet />
          </main>
        </div>
      </div>
    </ProtectedLayout>
  );
};

const App: React.FC = () => {
  return (
    <ErrorBoundary>
      <Suspense fallback={<div>Loading...</div>}>
        <Routes>
          <Route path="/login" element={<Login />} />
          <Route path="/signup" element={<SignUp />} />
          <Route element={<ClerkProtectedLayout />}>
            <Route path="/" element={<Dashboard />} />
            <Route path="/settings" element={<Settings />} />
            <Route path="/users" element={<Users />} />
            <Route path="/roles" element={<Roles />} />
            <Route path="/permissions" element={<Permissions />} />
            <Route path="/access-insights" element={<AccessInsights />} />
            <Route path="/integration-setup" element={<IntegrationSetup />} />
            <Route path="/processing-status" element={<ProcessingStatusDashboard />} />
          </Route>
          <Route path="*" element={<NotFound />} />
        </Routes>
      </Suspense>
    </ErrorBoundary>
  );
};

export default App;