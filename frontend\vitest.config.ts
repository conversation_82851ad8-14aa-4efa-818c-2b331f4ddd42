import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';
import { vanillaExtractPlugin } from '@vanilla-extract/vite-plugin';
import { resolve } from 'path';
import { supabase } from './src/services/supabaseClient';

export default defineConfig({
  plugins: [
    vanillaExtractPlugin(),
    react(),
  ],
  test: {
    globals: true,
    environment: 'jsdom', // Default environment
    setupFiles: ['./src/test/setup.ts'],
    include: ['**/*.{test,spec}.{ts,tsx}'],
    coverage: {
      reporter: ['text', 'json', 'html'],
    },
    env: {
      VITE_SUPABASE_URL: 'https://test-supabase-url.com',
      VITE_SUPABASE_ANON_KEY: 'test-anon-key',
    },
  },
  workspace: [
    {
      include: ['**/browser/**/*.{test,spec}.{ts,tsx}'],
      test: {
        environment: 'jsdom',
      },
    },
    {
      include: ['**/server/**/*.{test,spec}.{ts,tsx}'],
      test: {
        environment: 'node',
      },
    },
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
    },
  },
});