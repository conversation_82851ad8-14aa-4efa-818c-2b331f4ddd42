import { Navigate } from 'react-router-dom';
import { SignIn, useAuth } from '@clerk/clerk-react';
import * as styles from './Login.css';

const Login: React.FC = () => {
  const { isLoaded, isSignedIn } = useAuth();

  // Redirect if already logged in
  if (isLoaded && isSignedIn) {
    return <Navigate to="/" replace />;
  }

  return (
    <div className={styles.container}>
      <div className={styles.card}>
        <h1>Login to Rolewise.ai</h1>
        <div className={styles.clerkContainer}>
          <SignIn
            routing="path"
            path="/login"
            signUpUrl="/signup"
            redirectUrl="/"
          />
        </div>
      </div>
    </div>
  );
};

export default Login;