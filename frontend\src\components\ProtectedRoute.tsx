import { Navigate, Outlet, useNavigate } from 'react-router-dom';
import { useAuthStore } from '../stores/authStore';
import * as styles from './layoutStyles.css.ts';
import { useEffect, useState } from 'react';

interface ProtectedRouteProps {
  children?: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { user, loading, userDetails, signOut } = useAuthStore();
  const [timeoutReached, setTimeoutReached] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    // Set a timeout to show an error message if loading takes too long
    const timer = setTimeout(() => {
      if (loading) {
        setTimeoutReached(true);
      }
    }, 3000); // 3 seconds timeout

    return () => clearTimeout(timer);
  }, [loading]);

  // Force reset loading state after a certain time
  useEffect(() => {
    const forceResetTimer = setTimeout(() => {
      if (loading) {
        // This is a workaround to prevent infinite loading
        const currentState = useAuthStore.getState();
        if (currentState.loading) {
          useAuthStore.setState({ loading: false });
        }
      }
    }, 5000); // 5 seconds timeout

    return () => clearTimeout(forceResetTimer);
  }, [loading]);

  const handleSignOut = async () => {
    try {
      await signOut();
      navigate('/login');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  if (loading && !timeoutReached) {
    return (
      <div className={styles.loading}>
        <div className={styles.spinnerStyle} />
        <p>Loading your profile...</p>
      </div>
    );
  }

  if (loading && timeoutReached) {
    return (
      <div className={styles.errorMessageStyle}>
        <h2>Loading Timeout</h2>
        <p>It's taking longer than expected to load your profile. Please try refreshing the page or signing out and back in.</p>
        <button onClick={handleSignOut} className={styles.signOutButtonStyle}>Sign Out</button>
      </div>
    );
  }

  if (!user) {
    return <Navigate to="/login" replace />;
  }

  if (!userDetails) {
    return (
      <div className={styles.errorMessageStyle}>
        <h2>Profile Error</h2>
        <p>Unable to load your profile. Please try signing out and signing in again.</p>
        <button onClick={handleSignOut} className={styles.signOutButtonStyle}>Sign Out</button>
      </div>
    );
  }

  return children ? <>{children}</> : <Outlet />;
};

export default ProtectedRoute;