-- Migration to add role feedback table for manual override capabilities
-- This table will store feedback from administrators when they manually adjust roles

-- Create role_feedback table
CREATE TABLE IF NOT EXISTS public.role_feedback (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  role_id UUID NOT NULL REFERENCES public.roles(id) ON DELETE CASCADE,
  tenant_id UUID NOT NULL REFERENCES public.tenants(id) ON DELETE CASCADE,
  feedback_text TEXT NOT NULL,
  feedback_type TEXT NOT NULL CHECK (feedback_type IN ('manual_override', 'suggestion', 'correction')),
  metadata JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id)
);

-- Add comment to explain the purpose of the table
COMMENT ON TABLE public.role_feedback IS 'Stores feedback from administrators when they manually adjust roles';

-- Enable Row Level Security
ALTER TABLE public.role_feedback ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for the role_feedback table
CREATE POLICY "Users can view role feedback for their tenants" ON public.role_feedback
  FOR SELECT
  USING (
    tenant_id IN (
      SELECT tenant_id FROM public.user_tenants
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert role feedback for their tenants" ON public.role_feedback
  FOR INSERT
  WITH CHECK (
    tenant_id IN (
      SELECT tenant_id FROM public.user_tenants
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Service role can do anything with role_feedback" ON public.role_feedback
  USING (auth.role() = 'service_role')
  WITH CHECK (auth.role() = 'service_role');

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_role_feedback_role_id
  ON public.role_feedback (role_id);

CREATE INDEX IF NOT EXISTS idx_role_feedback_tenant_id
  ON public.role_feedback (tenant_id);

-- Add trigger to set created_by to current user
CREATE OR REPLACE FUNCTION set_role_feedback_created_by()
RETURNS TRIGGER AS $$
BEGIN
  NEW.created_by = auth.uid();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER set_role_feedback_created_by_trigger
BEFORE INSERT ON public.role_feedback
FOR EACH ROW
EXECUTE FUNCTION set_role_feedback_created_by();
