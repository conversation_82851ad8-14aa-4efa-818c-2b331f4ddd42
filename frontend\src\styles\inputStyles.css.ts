import { style } from '@vanilla-extract/css';

export const inputStyle = style({
  width: '100%',
  padding: '0.5rem',
  border: '1px solid #d1d5db', // Equivalent to border-gray-300
  borderRadius: '0.375rem',
  boxSizing: 'border-box',
  fontSize: '1rem', // Equivalent to text-base
  transition: 'all 0.2s',
  ':focus': {
    outline: '2px solid #3b82f6', // Equivalent to focus:ring-2 focus:ring-primary-500
    borderColor: 'transparent',
  },
  ':disabled': {
    backgroundColor: '#f3f4f6', // Equivalent to bg-gray-100
    borderColor: '#e5e7eb', // Equivalent to border-gray-200
    color: '#6b7280', // Equivalent to text-gray-500
    cursor: 'not-allowed',
    opacity: 0.7,
  },
  '::placeholder': {
    color: '#9ca3af', // Equivalent to placeholder-gray-400
  },
});