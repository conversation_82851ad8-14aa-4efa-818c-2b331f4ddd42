import { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useAuthStore } from '../stores/authStore';
import * as styles from './sidebarStyles.css.ts';

interface SidebarProps {
  onToggle?: (isOpen: boolean) => void;
}

const Sidebar = ({ onToggle }: SidebarProps) => {
  const { user } = useAuthStore();
  const location = useLocation();
  const [isOpen, setIsOpen] = useState(true);

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  const toggleSidebar = () => {
    const newState = !isOpen;
    setIsOpen(newState);
    if (onToggle) {
      onToggle(newState);
    }
  };

  // Notify parent component of initial state and when isOpen changes
  useEffect(() => {
    if (onToggle) {
      onToggle(isOpen);
    }
  }, [isOpen, onToggle]);

  return (
    <>
      {/* Hamburger button for mobile */}
      <button
        className={styles.sidebarToggleIndicatorStyle}
        onClick={toggleSidebar}
        aria-label="Toggle sidebar"
      >
        {isOpen ? '←' : '→'}
      </button>

      <div className={`${styles.sidebarBaseStyle} ${isOpen ? styles.sidebarOpenStyle : styles.sidebarClosedStyle}`}>
        <button
          className={styles.toggleButtonStyle}
          onClick={toggleSidebar}
          aria-label="Toggle sidebar"
        >
          {isOpen ? '←' : '→'}
        </button>
        <div className={styles.sidebarContentStyle}>
          <nav className={styles.nav}>
            <Link
              to="/"
              className={`${styles.navItem} ${isActive('/') ? styles.active : ''}`}
            >
              <span className={styles.iconStyle}>📊</span>
              <span className={styles.navItemTextStyle}>Dashboard</span>
            </Link>
            <Link
              to="/users"
              className={`${styles.navItem} ${isActive('/users') ? styles.active : ''}`}
            >
              <span className={styles.iconStyle}>👥</span>
              <span className={styles.navItemTextStyle}>Users</span>
            </Link>
            <Link
              to="/roles"
              className={`${styles.navItem} ${isActive('/roles') ? styles.active : ''}`}
            >
              <span className={styles.iconStyle}>🔑</span>
              <span className={styles.navItemTextStyle}>Roles</span>
            </Link>
            <Link
              to="/permissions"
              className={`${styles.navItem} ${isActive('/permissions') ? styles.active : ''}`}
            >
              <span className={styles.iconStyle}>🔒</span>
              <span className={styles.navItemTextStyle}>Permissions</span>
            </Link>
            <Link
              to="/access-insights"
              className={`${styles.navItem} ${isActive('/access-insights') ? styles.active : ''}`}
            >
              <span className={styles.iconStyle}>📈</span>
              <span className={styles.navItemTextStyle}>Access Insights</span>
            </Link>
            <Link
              to="/integration-setup"
              className={`${styles.navItem} ${isActive('/integration-setup') ? styles.active : ''}`}
            >
              <span className={styles.iconStyle}>⚙️</span>
              <span className={styles.navItemTextStyle}>Integration Setup</span>
            </Link>
            <Link
              to="/processing-status"
              className={`${styles.navItem} ${isActive('/processing-status') ? styles.active : ''}`}
            >
              <span className={styles.iconStyle}>🔄</span>
              <span className={styles.navItemTextStyle}>Processing Status</span>
            </Link>
          </nav>
        </div>
      </div>
    </>
  );
};

export default Sidebar;