import { create } from 'zustand';
import { supabase } from '../services/supabaseClient';
import { RolewiseRole } from '../types/rolewise';
import { SupabaseApplication, SupabaseGroup, SupabaseRoleApplicationMapping } from '../types/supabase';
import debounce from 'lodash/debounce';

interface ProcessingJob {
  id: string;
  tenant_id: string;
  status: 'pending' | 'fetching' | 'processing' | 'inferring' | 'completed' | 'failed';
  started_at: string;
  completed_at: string | null;
  error: string | null;
  metadata: Record<string, any>;
}

interface RolewiseState {
  applications: SupabaseApplication[];
  groups: SupabaseGroup[];
  roles: RolewiseRole[];
  roleApplicationMappings: SupabaseRoleApplicationMapping[];
  loading: boolean;
  error: string | null;
  errorAcknowledged: boolean;
  currentJobId: string | null;
  currentJobStatus: string | null;
  processingJobs: ProcessingJob[];
  processingJobsLoading: boolean;
  selectedJobId: string | null;
  processTenantData: (tenantId: string, runInference?: boolean, forceRefresh?: boolean) => Promise<void>;
  checkJobStatus: (jobId: string) => Promise<ProcessingJob | null>;
  acknowledgeError: () => void;
  fetchRoleApplicationMappings: (tenantId: string, roleId?: string) => Promise<void>;
  fetchProcessingJobs: (tenantId: string, limit?: number) => Promise<void>;
  selectJob: (jobId: string | null) => void;
  updateRole: (role: RolewiseRole, feedback?: string) => Promise<void>;
  submitRoleFeedback: (roleId: string, feedback: string) => Promise<void>;
}

type RolewiseStore = RolewiseState;

// Use a module-level variable to track if a job is in progress
let isProcessing = false;
// Track active polling timers
let activePollingTimers: NodeJS.Timeout[] = [];

const processTenantDataImpl = async (
  tenantId: string,
  runInference: boolean = true,
  forceRefresh: boolean = false,
  set: (state: Partial<RolewiseState>) => void,
  get: () => RolewiseState
) => {
  // Allow force refresh to bypass the loading check
  if (isProcessing && !forceRefresh) {
    console.log('processTenantData blocked: job already in progress');
    return;
  }

  if ((get().loading && !forceRefresh) || (get().error && !get().errorAcknowledged)) {
    console.log('processTenantData blocked: loading or unacknowledged error');
    return;
  }

  // Set the processing flag to prevent multiple concurrent runs
  isProcessing = true;

  try {
    set({ loading: true, error: null });

    const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
    if (sessionError || !sessionData.session?.access_token) {
      throw new Error('Authentication failed: Unable to obtain valid session');
    }
    const accessToken = sessionData.session.access_token;

    console.log('Invoking orchestrate-tenant-processing with:', {
      tenantId,
      runInference,
      forceRefresh,
      accessToken: accessToken.slice(0, 10) + '...',
    });

    const maxRetries = 3;
    let orchestrationError;

    for (let retryCount = 0; retryCount < maxRetries; retryCount++) {
      const { data, error } = await supabase.functions.invoke('orchestrate-tenant-processing', {
        body: { tenantId, runInference, forceRefresh },
        headers: { Authorization: `Bearer ${accessToken}` },
      });

      orchestrationError = error;

      if (!error) {
        console.log('Orchestration response:', data);

        // If we got a job ID, store it
        if (data.jobId) {
          set({
            currentJobId: data.jobId,
            currentJobStatus: data.status || 'pending'
          });

          // If the job is already completed, update the UI
          if (data.status === 'completed') {
            await fetchAndUpdateData(tenantId, set);
          } else {
            // Start polling for job status
            startJobStatusPolling(data.jobId, tenantId, set, get);
          }
        }

        // If we got application data directly, update the UI
        if (data.applications) {
          set({
            applications: data.applications.map((app: any) => ({
              id: app.id,
              name: app.displayName || app.name,
              description: app.description || '',
            })),
          });
        }

        // If we got group data directly, update the UI
        if (data.groups) {
          set({
            groups: data.groups.map((group: any) => ({
              id: group.groupId || group.id,
              display_name: group.displayName || group.display_name,
              description: group.description || '',
            })),
          });
        }

        // Set loading to false only if we're not waiting for a job
        if (!data.jobId || data.status === 'completed') {
          set({ loading: false });
        }
        return;
      }

      console.warn(`Retry ${retryCount + 1}/${maxRetries} failed:`, error);
      await new Promise((resolve) => setTimeout(resolve, 1000 * Math.pow(2, retryCount)));
    }

    console.error('Orchestration error:', orchestrationError);
    let errorMessage = orchestrationError?.message || 'Unknown error';

    try {
      // Parse the response body from the Edge Function
      const responseBody = await orchestrationError?.context?.response?.json();
      if (responseBody) {
        errorMessage = responseBody.error || errorMessage;
        if (responseBody.details) {
          errorMessage += `: ${responseBody.details}`;
        }
      }
    } catch (e) {
      console.warn('Failed to parse error response:', e);
    }

    throw new Error(`Failed to process tenant data: ${errorMessage}`);
  } catch (error) {
    const errorMessage = error instanceof Error
      ? error.message
      : 'An unexpected error occurred while processing tenant data';

    console.error('Error processing tenant data:', {
      error: errorMessage,
      tenantId,
      timestamp: new Date().toISOString(),
    });

    set({
      loading: false,
      error: errorMessage,
      errorAcknowledged: false,
    });

    if (errorMessage.includes('Authentication failed')) {
      console.warn('Authentication error detected');
    } else if (errorMessage.includes('Rate limit')) {
      console.warn('Rate limit error detected');
    }
  } finally {
    isProcessing = false;
  }
};

// Helper function to fetch data after job completion
const fetchAndUpdateData = async (
  tenantId: string,
  set: (state: Partial<RolewiseState>) => void
) => {
  try {
    // Fetch applications
    const { data: applications, error: appsError } = await supabase
      .from('applications')
      .select('*')
      .eq('tenant_id', tenantId);

    if (appsError) {
      console.error('Error fetching applications:', appsError);
    } else if (applications) {
      set({ applications });
    }

    // Fetch groups
    const { data: groups, error: groupsError } = await supabase
      .from('groups')
      .select('*')
      .eq('tenant_id', tenantId);

    if (groupsError) {
      console.error('Error fetching groups:', groupsError);
    } else if (groups) {
      set({ groups });
    }

    // Fetch roles
    const { data: roles, error: rolesError } = await supabase
      .from('roles')
      .select('*')
      .eq('tenant_id', tenantId);

    if (rolesError) {
      console.error('Error fetching roles:', rolesError);
    } else if (roles) {
      set({
        roles: roles.map(role => ({
          id: role.id,
          name: role.name,
          description: role.description || '',
          tenantId: role.tenant_id,
          source: role.source,
          inferenceMethod: role.inference_method,
          inferenceVersion: role.inference_version,
          inferenceDate: role.inference_date,
          confidenceScore: role.confidence_score,
          metadata: role.metadata,
          createdAt: role.created_at,
          updatedAt: role.updated_at
        }))
      });
    }

    // Fetch role-application mappings
    const { data: mappings, error: mappingsError } = await supabase
      .from('role_application_mappings')
      .select('*, applications(name, description), roles(name, description)')
      .eq('tenant_id', tenantId);

    if (mappingsError) {
      console.error('Error fetching role-application mappings:', mappingsError);
    } else if (mappings) {
      set({ roleApplicationMappings: mappings });
    }

    set({ loading: false });
  } catch (error) {
    console.error('Error fetching data after job completion:', error);
    set({ loading: false });
  }
};

// Clear all active polling timers
const clearAllPollingTimers = () => {
  activePollingTimers.forEach(timer => clearTimeout(timer));
  activePollingTimers = [];
};

// Poll for job status
const startJobStatusPolling = async (
  jobId: string,
  tenantId: string,
  set: (state: Partial<RolewiseState>) => void,
  get: () => RolewiseState
) => {
  // Clear any existing polling timers
  clearAllPollingTimers();

  const pollInterval = 2000; // 2 seconds
  const maxPolls = 30; // Maximum number of polls (60 seconds total)
  let pollCount = 0;

  const poll = async () => {
    // Check if we should stop polling (e.g., if component was unmounted)
    if (!get().currentJobId || get().currentJobId !== jobId) {
      console.log('Stopping polling because job ID changed or was cleared');
      return;
    }

    if (pollCount >= maxPolls) {
      console.warn('Max polling attempts reached');
      set({
        loading: false,
        currentJobStatus: 'unknown',
        error: 'Processing is taking longer than expected. Please check back later.'
      });
      return;
    }

    pollCount++;

    try {
      const { data: job, error } = await supabase
        .from('processing_jobs')
        .select('*')
        .eq('id', jobId)
        .single();

      if (error) {
        console.error('Error polling job status:', error);
        return;
      }

      if (!job) {
        console.warn('Job not found:', jobId);
        return;
      }

      // Only update status if it's different, to avoid unnecessary re-renders
      if (job.status !== get().currentJobStatus) {
        set({ currentJobStatus: job.status });
      }

      if (job.status === 'completed') {
        console.log('Job completed successfully');
        await fetchAndUpdateData(tenantId, set);
        return;
      } else if (job.status === 'failed') {
        console.error('Job failed:', job.error);
        set({
          loading: false,
          error: `Processing failed: ${job.error || 'Unknown error'}`
        });
        return;
      }

      // Continue polling
      const timer = setTimeout(poll, pollInterval);
      activePollingTimers.push(timer);
    } catch (error) {
      console.error('Error in polling:', error);
      const timer = setTimeout(poll, pollInterval);
      activePollingTimers.push(timer);
    }
  };

  // Start polling
  const timer = setTimeout(poll, pollInterval);
  activePollingTimers.push(timer);
};

const debouncedProcessTenantData = debounce(
  (
    tenantId: string,
    runInference: boolean,
    forceRefresh: boolean,
    set: (state: Partial<RolewiseState>) => void,
    callback: () => void
  ) => {
    const get = useRolewiseStore.getState;
    processTenantDataImpl(tenantId, runInference, forceRefresh, set, get).then(callback);
  },
  1000,
  { leading: true, trailing: false }
);

export const useRolewiseStore = create<RolewiseStore>((set, get) => ({
  applications: [],
  groups: [],
  roles: [],
  roleApplicationMappings: [],
  loading: false,
  error: null,
  errorAcknowledged: false,
  currentJobId: null,
  currentJobStatus: null,
  processingJobs: [],
  processingJobsLoading: false,
  selectedJobId: null,

  processTenantData: async (tenantId: string, runInference = true, forceRefresh = false) => {
    console.log('processTenantData called with tenantId:', tenantId, 'runInference:', runInference, 'forceRefresh:', forceRefresh, 'at:', new Date().toISOString());
    return new Promise<void>((resolve) => {
      debouncedProcessTenantData(tenantId, runInference, forceRefresh, set, () => resolve());
    });
  },

  checkJobStatus: async (jobId: string): Promise<ProcessingJob | null> => {
    try {
      const { data, error } = await supabase
        .from('processing_jobs')
        .select('*')
        .eq('id', jobId)
        .single();

      if (error) {
        console.error('Error checking job status:', error);
        return null;
      }

      // Only update status if it's different
      if (data.status !== get().currentJobStatus) {
        set({ currentJobStatus: data.status });
      }

      return data as ProcessingJob;
    } catch (error) {
      console.error('Error in checkJobStatus:', error);
      return null;
    }
  },

  acknowledgeError: () => {
    set({ errorAcknowledged: true, error: null });
  },

  fetchRoleApplicationMappings: async (tenantIdOrRoleId: string, roleId?: string) => {
    try {
      set({ loading: true });

      // If only one parameter is provided, assume it's the roleId and use the current tenant
      let tenantId = tenantIdOrRoleId;
      let roleIdToUse = roleId;

      if (!roleId) {
        // If no roleId is provided, the first parameter is the roleId
        roleIdToUse = tenantIdOrRoleId;
        // Get the current tenant ID from the roles
        const firstRole = get().roles[0];
        if (firstRole) {
          tenantId = firstRole.tenantId;
        } else {
          console.error('No roles found to determine tenant ID');
          set({
            error: 'No roles found to determine tenant ID',
            loading: false
          });
          return;
        }
      }

      let query = supabase
        .from('role_application_mappings')
        .select('*, applications(name, description), roles(name, description)')
        .eq('tenant_id', tenantId);

      if (roleIdToUse) {
        query = query.eq('role_id', roleIdToUse);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching role-application mappings:', error);
        set({
          error: `Failed to fetch role-application mappings: ${error.message}`,
          loading: false
        });
        return;
      }

      set({
        roleApplicationMappings: data || [],
        loading: false
      });
    } catch (error) {
      const errorMessage = error instanceof Error
        ? error.message
        : 'An unexpected error occurred while fetching role-application mappings';

      console.error('Error fetching role-application mappings:', errorMessage);
      set({
        loading: false,
        error: errorMessage,
        errorAcknowledged: false,
      });
    }
  },

  fetchProcessingJobs: async (tenantId: string, limit = 10) => {
    try {
      set({ processingJobsLoading: true });

      const { data, error } = await supabase
        .from('processing_jobs')
        .select('*')
        .eq('tenant_id', tenantId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Error fetching processing jobs:', error);
        set({
          error: `Failed to fetch processing jobs: ${error.message}`,
          processingJobsLoading: false
        });
        return;
      }

      set({
        processingJobs: data || [],
        processingJobsLoading: false
      });
    } catch (error) {
      const errorMessage = error instanceof Error
        ? error.message
        : 'An unexpected error occurred while fetching processing jobs';

      console.error('Error fetching processing jobs:', errorMessage);
      set({
        processingJobsLoading: false,
        error: errorMessage,
        errorAcknowledged: false,
      });
    }
  },

  selectJob: (jobId: string | null) => {
    set({ selectedJobId: jobId });
  },

  updateRole: async (role: RolewiseRole, feedback?: string) => {
    try {
      set({ loading: true });

      // Prepare the role data for update
      const roleData = {
        id: role.id,
        name: role.name,
        description: role.description,
        tenant_id: role.tenantId,
        source: role.source || 'manual',
        confidence_score: role.confidenceScore,
        updated_at: new Date().toISOString()
      };

      // Update the role in the database
      const { error } = await supabase
        .from('roles')
        .update(roleData)
        .eq('id', role.id);

      if (error) {
        console.error('Error updating role:', error);
        set({
          error: `Failed to update role: ${error.message}`,
          loading: false
        });
        return;
      }

      // If feedback is provided, submit it
      if (feedback) {
        await get().submitRoleFeedback(role.id, feedback);
      }

      // Update the role in the local state
      const updatedRoles = get().roles.map(r =>
        r.id === role.id ? role : r
      );

      set({
        roles: updatedRoles,
        loading: false
      });

      console.log('Role updated successfully:', role.name);
    } catch (error) {
      const errorMessage = error instanceof Error
        ? error.message
        : 'An unexpected error occurred while updating the role';

      console.error('Error updating role:', errorMessage);
      set({
        loading: false,
        error: errorMessage,
        errorAcknowledged: false,
      });
    }
  },

  submitRoleFeedback: async (roleId: string, feedback: string) => {
    try {
      // Get the role from the state
      const role = get().roles.find(r => r.id === roleId);
      if (!role) {
        console.error('Role not found for feedback submission:', roleId);
        return;
      }

      // Prepare the feedback data
      const feedbackData = {
        role_id: roleId,
        tenant_id: role.tenantId,
        feedback_text: feedback,
        feedback_type: 'manual_override',
        created_at: new Date().toISOString()
      };

      // Submit the feedback to the database
      const { error } = await supabase
        .from('role_feedback')
        .insert(feedbackData);

      if (error) {
        console.error('Error submitting role feedback:', error);
        // Don't set an error state here as this is a secondary operation
        return;
      }

      console.log('Role feedback submitted successfully');
    } catch (error) {
      console.error('Error submitting role feedback:', error);
      // Don't set an error state here as this is a secondary operation
    }
  },
}));