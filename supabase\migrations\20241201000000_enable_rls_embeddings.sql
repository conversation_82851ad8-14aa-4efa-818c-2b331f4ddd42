-- Migration to enable <PERSON><PERSON> on embedding_cache and role_embeddings tables
-- This ensures proper security for embedding data access

-- Enable <PERSON><PERSON> on embedding_cache table
ALTER TABLE public.embedding_cache ENABLE ROW LEVEL SECURITY;

-- Add comment to explain the purpose of the table
COMMENT ON TABLE public.embedding_cache IS 'Stores cached embeddings to avoid re-computation. Global cache accessible to all authenticated users for reading.';

-- <PERSON>reate indexes for better performance on embedding_cache
CREATE INDEX IF NOT EXISTS idx_embedding_cache_text_model
  ON public.embedding_cache (text, model);

CREATE INDEX IF NOT EXISTS idx_embedding_cache_created_at
  ON public.embedding_cache (created_at);

-- RLS Policies for embedding_cache table

-- Service role can do anything with embedding_cache (for edge functions)
DROP POLICY IF EXISTS "Service role can do anything with embedding_cache" ON public.embedding_cache;
CREATE POLICY "Service role can do anything with embedding_cache"
  ON public.embedding_cache
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- Authenticated users can read from embedding cache (global cache)
DROP POLICY IF EXISTS "Authenticated users can read embedding cache" ON public.embedding_cache;
CREATE POLICY "Authenticated users can read embedding cache"
  ON public.embedding_cache
  FOR SELECT
  TO authenticated
  USING (true);

-- Only service role can insert/update/delete cache entries
DROP POLICY IF EXISTS "Only service role can modify embedding cache" ON public.embedding_cache;
CREATE POLICY "Only service role can modify embedding cache"
  ON public.embedding_cache
  FOR INSERT
  TO service_role
  WITH CHECK (true);

DROP POLICY IF EXISTS "Only service role can update embedding cache" ON public.embedding_cache;
CREATE POLICY "Only service role can update embedding cache"
  ON public.embedding_cache
  FOR UPDATE
  TO service_role
  USING (true)
  WITH CHECK (true);

DROP POLICY IF EXISTS "Only service role can delete embedding cache" ON public.embedding_cache;
CREATE POLICY "Only service role can delete embedding cache"
  ON public.embedding_cache
  FOR DELETE
  TO service_role
  USING (true);

-- RLS Policies for role_embeddings table (RLS already enabled)

-- Add comment to explain the purpose of the table
COMMENT ON TABLE public.role_embeddings IS 'Stores embeddings for roles, scoped by tenant for security';

-- Create indexes for better performance on role_embeddings
CREATE INDEX IF NOT EXISTS idx_role_embeddings_tenant_id
  ON public.role_embeddings (tenant_id);

CREATE INDEX IF NOT EXISTS idx_role_embeddings_role_id
  ON public.role_embeddings (role_id);

-- Service role can do anything with role_embeddings (for edge functions)
DROP POLICY IF EXISTS "Service role can do anything with role_embeddings" ON public.role_embeddings;
CREATE POLICY "Service role can do anything with role_embeddings"
  ON public.role_embeddings
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- Users can read role embeddings for their tenants
DROP POLICY IF EXISTS "Users can read role embeddings for their tenants" ON public.role_embeddings;
CREATE POLICY "Users can read role embeddings for their tenants"
  ON public.role_embeddings
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.user_tenants ut
      WHERE ut.user_id = auth.uid()
      AND ut.tenant_id = role_embeddings.tenant_id
    )
  );

-- Only service role can insert/update/delete role embeddings
DROP POLICY IF EXISTS "Only service role can modify role embeddings" ON public.role_embeddings;
CREATE POLICY "Only service role can modify role embeddings"
  ON public.role_embeddings
  FOR INSERT
  TO service_role
  WITH CHECK (true);

DROP POLICY IF EXISTS "Only service role can update role embeddings" ON public.role_embeddings;
CREATE POLICY "Only service role can update role embeddings"
  ON public.role_embeddings
  FOR UPDATE
  TO service_role
  USING (true)
  WITH CHECK (true);

DROP POLICY IF EXISTS "Only service role can delete role embeddings" ON public.role_embeddings;
CREATE POLICY "Only service role can delete role embeddings"
  ON public.role_embeddings
  FOR DELETE
  TO service_role
  USING (true);
