import { useState, useEffect } from 'react';
import { Link, Navigate } from 'react-router-dom';
import { useAuthStore } from '../stores/authStore';
import { AuthStatus, RegisterCredentials } from '../types/auth';
import * as styles from './registerStyles.css.ts';

const Register: React.FC = () => {
  const { user, status, signUp } = useAuthStore();
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [registrationStep, setRegistrationStep] = useState<'form' | 'confirmation' | 'complete'>('form');

  // Redirect if already logged in
  if (user && status === AuthStatus.AUTHENTICATED) {
    return <Navigate to="/" replace />;
  }

  const handleSubmit = async (e: React.FormEvent): Promise<void> => {
    e.preventDefault();
    setError(null);

    if (password !== confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    setIsSubmitting(true);

    try {
      const credentials: RegisterCredentials = {
        email,
        password,
        name,
      };

      const { error: signUpError, data: userData } = await signUp(credentials);
      
      if (signUpError) {
        setError(signUpError.message);
      } else if (userData) {
        setSuccessMessage('Registration successful! Please check your email to confirm your account.');
        setRegistrationStep('confirmation');
      }
    } catch (err) {
      setError('An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderForm = (): JSX.Element => (
    <form onSubmit={handleSubmit} className={styles.form}>
      <div className={styles.formGroup}>
        <label htmlFor="name">Name</label>
        <input
          id="name"
          type="text"
          value={name}
          onChange={(e) => setName(e.target.value)}
          required
          className={styles.input}
        />
      </div>
      <div className={styles.formGroup}>
        <label htmlFor="email">Email</label>
        <input
          id="email"
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
          className={styles.input}
        />
      </div>
      <div className={styles.formGroup}>
        <label htmlFor="password">Password</label>
        <input
          id="password"
          type="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          required
          className={styles.input}
        />
      </div>
      <div className={styles.formGroup}>
        <label htmlFor="confirmPassword">Confirm Password</label>
        <input
          id="confirmPassword"
          type="password"
          value={confirmPassword}
          onChange={(e) => setConfirmPassword(e.target.value)}
          required
          className={styles.input}
        />
      </div>
      <button
        type="submit"
        disabled={isSubmitting}
        className={styles.submitButton}
      >
        {isSubmitting ? 'Registering...' : 'Register'}
      </button>
      {error && <div className={styles.error}>{error}</div>}
      <div className={styles.links}>
        Already have an account? <Link to="/login">Login</Link>
      </div>
    </form>
  );

  const renderConfirmation = (): JSX.Element => (
    <div className={styles.confirmation}>
      <h2>Check Your Email</h2>
      <p>{successMessage}</p>
      <Link to="/login" className={styles.loginLink}>
        Return to Login
      </Link>
    </div>
  );

  return (
    <div className={styles.container}>
      <div className={styles.card}>
        <h1>Create Account</h1>
        {registrationStep === 'form' ? renderForm() : renderConfirmation()}
      </div>
    </div>
  );
};

export default Register;