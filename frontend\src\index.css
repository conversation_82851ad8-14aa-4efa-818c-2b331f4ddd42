* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubunt<PERSON>', 'Can<PERSON><PERSON>', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f9fafb; /* Equivalent to bg-gray-50 */
  color: #1f2937; /* Equivalent to text-gray-900 */
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

.btn {
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  transition: background-color 0.2s;
}

.btn-primary {
  background-color: #3b82f6; /* Equivalent to bg-primary-600 */
  color: white;
}

.btn-primary:hover {
  background-color: #2563eb; /* Equivalent to hover:bg-primary-700 */
}

.btn-outline {
  border: 1px solid #d1d5db; /* Equivalent to border-gray-300 */
  background-color: white;
  color: #374151; /* Equivalent to text-gray-700 */
}

.btn-outline:hover {
  background-color: #f3f4f6; /* Equivalent to hover:bg-gray-50 */
}

.card {
  background-color: white;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  padding: 1.5rem; /* Equivalent to p-6 */
}

.input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #d1d5db; /* Equivalent to border-gray-300 */
  border-radius: 0.375rem;
}

.input:focus {
  outline: 2px solid #3b82f6; /* Equivalent to focus:ring-2 focus:ring-primary-500 */
  border-color: transparent;
}